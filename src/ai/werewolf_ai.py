"""
狼人AI策略
实现狼人的伪装策略、队友配合和欺骗能力
"""
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from ..models.enums import Role, GamePhase, VoteType, Faction
from ..models.game_state import GameState
from ..models.player import PlayerInfo
from ..players.ai_player import AIPlayer
from .llm_integration import <PERSON>MManager, create_llm_manager


@dataclass
class WerewolfTeamData:
    """狼人团队数据"""
    teammates: List[int] = field(default_factory=list)
    team_strategy: str = "aggressive"  # "aggressive", "defensive", "balanced"
    target_priority: List[int] = field(default_factory=list)
    fake_identity: Optional[Role] = None  # 伪装身份
    coordination_plan: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DeceptionStrategy:
    """欺骗策略"""
    fake_suspicions: List[int] = field(default_factory=list)  # 假装怀疑的玩家
    fake_trust: List[int] = field(default_factory=list)      # 假装信任的玩家
    false_information: List[str] = field(default_factory=list)  # 散布的假信息
    cover_story: str = ""  # 掩护故事


class WerewolfAI:
    """狼人AI策略"""
    
    def __init__(self, ai_player: AIPlayer, difficulty: str = "normal",
                use_llm: bool = True, llm_config: str = "qwen3_30b"):
        """初始化狼人AI"""
        self.ai_player = ai_player
        self.difficulty = difficulty
        self.team_data = WerewolfTeamData()
        self.deception = DeceptionStrategy()
        self.use_llm = use_llm
        
        # 初始化大模型
        if use_llm:
            self.llm_manager = create_llm_manager(llm_config)
        else:
            self.llm_manager = None
        
        # 策略参数
        self.params = self._init_strategy_params()
        
        # 伪装身份
        self._choose_fake_identity()
    
    def _init_strategy_params(self) -> Dict[str, float]:
        """初始化策略参数"""
        params = {
            "aggression": 0.6,              # 攻击性
            "deception_skill": 0.7,         # 欺骗技巧
            "team_coordination": 0.8,       # 团队协调
            "risk_tolerance": 0.5,          # 风险容忍度
            "fake_suspicion_rate": 0.3,     # 假装怀疑的频率
            "target_priority_weight": 0.8,  # 目标优先级权重
            "self_preservation": 0.7,       # 自保意识
        }
        
        # 根据难度调整参数
        if self.difficulty == "easy":
            params["deception_skill"] = 0.4
            params["team_coordination"] = 0.5
            params["fake_suspicion_rate"] = 0.1
        elif self.difficulty == "hard":
            params["deception_skill"] = 0.9
            params["team_coordination"] = 0.9
            params["fake_suspicion_rate"] = 0.5
            params["risk_tolerance"] = 0.3
        
        return params
    
    def _choose_fake_identity(self):
        """选择伪装身份"""
        # 根据策略选择伪装身份
        fake_roles = [Role.VILLAGER, Role.GUARD, Role.HUNTER]
        self.team_data.fake_identity = random.choice(fake_roles)
    
    def initialize_team_data(self, game_state: GameState):
        """初始化团队数据"""
        # 找到所有狼人队友
        werewolves = game_state.get_werewolves()
        self.team_data.teammates = [w.player_id for w in werewolves 
                                  if w.player_id != self.ai_player.player_id]
        
        # 设置团队策略
        if len(werewolves) >= 3:
            self.team_data.team_strategy = "aggressive"
        elif len(werewolves) == 2:
            self.team_data.team_strategy = "balanced"
        else:
            self.team_data.team_strategy = "defensive"
    
    def make_kill_decision(self, game_state: GameState) -> Optional[int]:
        """做出杀人决策"""
        # 获取可杀死的目标
        targets = [p for p in game_state.get_alive_players() 
                  if not p.is_werewolf()]
        
        if not targets:
            return None
        
        # 计算目标优先级
        target_scores = self._calculate_kill_priority(game_state, targets)
        
        # 选择优先级最高的目标
        best_target = max(target_scores.items(), key=lambda x: x[1])[0]
        
        return best_target
    
    def _calculate_kill_priority(self, game_state: GameState, 
                               targets: List[PlayerInfo]) -> Dict[int, float]:
        """计算杀人目标优先级"""
        priority_scores = {}
        
        for target in targets:
            score = 0.5  # 基础分数
            
            # 根据角色调整优先级
            if target.role == Role.SEER:
                score += 0.4  # 预言家优先级很高
            elif target.role == Role.WITCH:
                score += 0.3  # 女巫优先级较高
            elif target.role == Role.GUARD:
                score += 0.2  # 守卫优先级中等
            elif target.role == Role.HUNTER:
                score -= 0.2  # 猎人优先级较低（会反击）
            
            # 根据威胁程度调整
            threat_level = self._assess_threat_level(game_state, target)
            score += threat_level * 0.3
            
            # 根据怀疑度调整（避免杀死怀疑自己的人，除非必要）
            suspicion_of_me = self._estimate_suspicion_of_me(game_state, target.player_id)
            if suspicion_of_me > 0.7:
                score += 0.2  # 如果他们很怀疑我，优先杀死
            
            # 确保在0-1范围内
            priority_scores[target.player_id] = max(0.1, min(1.0, score))
        
        return priority_scores
    
    def _assess_threat_level(self, game_state: GameState, target: PlayerInfo) -> float:
        """评估目标威胁等级"""
        threat = 0.5
        
        # 分析投票历史
        for round_info in game_state.rounds_history:
            for vote in round_info.votes:
                if vote.voter_id == target.player_id:
                    # 如果投票给狼人，威胁度增加
                    voted_player = game_state.players.get(vote.target_id)
                    if voted_player and voted_player.is_werewolf():
                        threat += 0.1
        
        return min(1.0, threat)
    
    def _estimate_suspicion_of_me(self, game_state: GameState, target_id: int) -> float:
        """估计目标对我的怀疑度"""
        # 简化实现，实际中可以分析发言和投票模式
        return random.uniform(0.3, 0.7)
    
    def make_vote_decision(self, game_state: GameState, vote_type: VoteType) -> Optional[int]:
        """做出投票决策"""
        if vote_type == VoteType.WEREWOLF_KILL:
            return self.make_kill_decision(game_state)
        
        elif vote_type == VoteType.ELIMINATION:
            return self._make_elimination_vote(game_state)
        
        return None
    
    def _make_elimination_vote(self, game_state: GameState) -> Optional[int]:
        """做出淘汰投票决策"""
        # 获取非狼人玩家
        targets = [p for p in game_state.get_alive_players() 
                  if not p.is_werewolf() and p.player_id != self.ai_player.player_id]
        
        if not targets:
            return None
        
        # 策略：投票给威胁最大但不会暴露自己的目标
        vote_scores = self._calculate_vote_scores(game_state, targets)
        
        # 选择得分最高的目标
        best_target = max(vote_scores.items(), key=lambda x: x[1])[0]
        
        return best_target
    
    def _calculate_vote_scores(self, game_state: GameState, 
                             targets: List[PlayerInfo]) -> Dict[int, float]:
        """计算投票分数"""
        vote_scores = {}
        
        for target in targets:
            score = 0.5
            
            # 优先投票给特殊角色
            if target.role in [Role.SEER, Role.WITCH, Role.GUARD]:
                score += 0.3
            
            # 避免投票给可能反击的角色
            if target.role == Role.HUNTER:
                score -= 0.2
            
            # 考虑团队协调
            if self._should_coordinate_vote(game_state, target.player_id):
                score += 0.2
            
            # 考虑伪装需要
            if self._helps_maintain_cover(game_state, target.player_id):
                score += 0.1
            
            vote_scores[target.player_id] = max(0.1, min(1.0, score))
        
        return vote_scores
    
    def _should_coordinate_vote(self, game_state: GameState, target_id: int) -> bool:
        """是否应该协调投票"""
        # 检查队友的投票倾向
        teammate_votes = 0
        for teammate_id in self.team_data.teammates:
            # 简化实现：假设队友也会投票给威胁目标
            if random.random() < 0.7:  # 70%概率协调
                teammate_votes += 1
        
        return teammate_votes > 0
    
    def _helps_maintain_cover(self, game_state: GameState, target_id: int) -> bool:
        """投票是否有助于维持伪装"""
        # 如果目标怀疑我，投票给他们有助于伪装
        suspicion = self._estimate_suspicion_of_me(game_state, target_id)
        return suspicion > 0.6
    
    def generate_speech(self, game_state: GameState, phase: GamePhase) -> str:
        """生成发言"""
        if phase != GamePhase.DAY_DISCUSSION:
            return "我没有什么要说的。"
        
        # 使用大模型生成发言
        if self.use_llm and self.llm_manager:
            context = {
                "player_id": self.ai_player.player_id,
                "fake_identity": self.team_data.fake_identity.name if self.team_data.fake_identity else "VILLAGER",
                "teammates": self.team_data.teammates,
                "deception_strategy": {
                    "fake_suspicions": self.deception.fake_suspicions,
                    "fake_trust": self.deception.fake_trust
                }
            }
            
            speech = self.llm_manager.generate_speech(
                role=Role.WEREWOLF,
                game_state=game_state,
                context=context
            )
            
            return speech
        
        # 基于规则生成发言
        return self._rule_based_speech_generation(game_state)
    
    def _rule_based_speech_generation(self, game_state: GameState) -> str:
        """基于规则生成发言"""
        # 选择发言策略
        strategy = random.choice([
            "deflect_suspicion",    # 转移怀疑
            "fake_analysis",        # 假分析
            "support_teammate",     # 支持队友
            "blend_in"             # 融入群体
        ])
        
        if strategy == "deflect_suspicion":
            return self._generate_deflection_speech(game_state)
        elif strategy == "fake_analysis":
            return self._generate_fake_analysis_speech(game_state)
        elif strategy == "support_teammate":
            return self._generate_support_speech(game_state)
        else:
            return self._generate_blend_speech(game_state)
    
    def _generate_deflection_speech(self, game_state: GameState) -> str:
        """生成转移怀疑的发言"""
        # 选择一个非队友作为怀疑目标
        targets = [p for p in game_state.get_alive_players() 
                  if not p.is_werewolf() and p.player_id != self.ai_player.player_id]
        
        if targets:
            target = random.choice(targets)
            speeches = [
                f"我觉得{target.name}的行为很可疑。",
                f"{target.name}的投票模式有问题。",
                f"大家注意{target.name}的发言，很奇怪。"
            ]
            return random.choice(speeches)
        
        return "我们需要仔细分析每个人的行为。"
    
    def _generate_fake_analysis_speech(self, game_state: GameState) -> str:
        """生成假分析发言"""
        speeches = [
            "根据昨晚的情况，我觉得狼人可能在误导我们。",
            "从投票模式来看，有人在故意混淆视听。",
            "我们要相信逻辑，不要被表象迷惑。",
            "狼人肯定在我们中间，要仔细观察。"
        ]
        return random.choice(speeches)
    
    def _generate_support_speech(self, game_state: GameState) -> str:
        """生成支持队友的发言"""
        if self.team_data.teammates:
            # 随机选择一个队友进行间接支持
            teammate_id = random.choice(self.team_data.teammates)
            teammate = game_state.players[teammate_id]
            
            speeches = [
                f"我觉得{teammate.name}的分析很有道理。",
                f"{teammate.name}一直表现得很正常。",
                f"我同意{teammate.name}的观点。"
            ]
            return random.choice(speeches)
        
        return "我们要团结起来找出狼人。"
    
    def _generate_blend_speech(self, game_state: GameState) -> str:
        """生成融入群体的发言"""
        speeches = [
            "我们要冷静分析，不要急躁。",
            "每个人的意见都很重要。",
            "让我们一起努力找出真相。",
            "我会支持大家的决定。",
            "希望能早日找出狼人。"
        ]
        return random.choice(speeches)
    
    def process_game_event(self, event_type: str, event_data: Dict[str, Any]):
        """处理游戏事件"""
        if event_type == "player_eliminated":
            self._process_elimination_event(event_data)
        elif event_type == "player_killed":
            self._process_kill_event(event_data)
        elif event_type == "vote_cast":
            self._process_vote_event(event_data)
    
    def _process_elimination_event(self, event_data: Dict[str, Any]):
        """处理淘汰事件"""
        player_id = event_data.get("player_id")
        
        if player_id in self.team_data.teammates:
            # 队友被淘汰，调整策略
            self.team_data.teammates.remove(player_id)
            
            # 如果队友数量减少，变得更加谨慎
            if len(self.team_data.teammates) <= 1:
                self.params["risk_tolerance"] *= 0.7
                self.params["self_preservation"] = min(1.0, self.params["self_preservation"] + 0.2)
    
    def _process_kill_event(self, event_data: Dict[str, Any]):
        """处理杀死事件"""
        # 分析杀死的目标，调整后续策略
        player_id = event_data.get("player_id")
        role = event_data.get("role")
        
        if role == Role.SEER:
            # 成功杀死预言家，降低风险
            self.params["risk_tolerance"] = min(1.0, self.params["risk_tolerance"] + 0.1)
    
    def _process_vote_event(self, event_data: Dict[str, Any]):
        """处理投票事件"""
        voter_id = event_data.get("voter_id")
        target_id = event_data.get("target_id")
        
        # 如果有人投票给我，记录并调整策略
        if target_id == self.ai_player.player_id:
            # 增加对投票者的假怀疑
            if voter_id not in self.deception.fake_suspicions:
                self.deception.fake_suspicions.append(voter_id)
    
    def get_team_coordination_plan(self, game_state: GameState) -> Dict[str, Any]:
        """获取团队协调计划"""
        plan = {
            "kill_target": self.make_kill_decision(game_state),
            "vote_target": self._make_elimination_vote(game_state),
            "strategy": self.team_data.team_strategy,
            "risk_level": "high" if self.params["risk_tolerance"] > 0.7 else "low"
        }
        
        return plan
